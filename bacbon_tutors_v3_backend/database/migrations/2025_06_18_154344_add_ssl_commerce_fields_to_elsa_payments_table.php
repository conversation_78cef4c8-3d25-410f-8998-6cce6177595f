<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('elsa_payments', function (Blueprint $table) {
            // SSL Commerce specific fields
            $table->string('val_id')->nullable()->after('tran_id');
            $table->string('bank_tran_id')->nullable()->after('val_id');
            $table->string('card_issuer')->nullable()->after('card_type');
            $table->string('card_brand')->nullable()->after('card_issuer');
            $table->string('card_sub_brand')->nullable()->after('card_brand');
            $table->string('card_issuer_country')->nullable()->after('card_sub_brand');
            $table->string('card_issuer_country_code')->nullable()->after('card_issuer_country');
            $table->decimal('store_amount', 10, 2)->nullable()->after('grand_total');
            $table->string('verify_sign')->nullable()->after('store_amount');
            $table->string('verify_key')->nullable()->after('verify_sign');
            $table->string('verify_sign_sha2')->nullable()->after('verify_key');
            $table->string('currency_type')->nullable()->after('currency');
            $table->decimal('currency_amount', 10, 2)->nullable()->after('currency_type');
            $table->decimal('currency_rate', 10, 4)->nullable()->after('currency_amount');
            $table->decimal('base_fair', 10, 2)->nullable()->after('currency_rate');
            $table->string('value_a')->nullable()->after('base_fair');
            $table->string('value_b')->nullable()->after('value_a');
            $table->string('value_c')->nullable()->after('value_b');
            $table->string('value_d')->nullable()->after('value_c');
            $table->string('risk_level')->nullable()->after('value_d');
            $table->string('risk_title')->nullable()->after('risk_level');
            $table->text('gateway_response')->nullable()->after('risk_title');
            $table->string('payment_processor')->nullable()->after('gateway_response');
            $table->json('ssl_response_data')->nullable()->after('payment_processor');

            // Add index for transaction ID for faster lookups
            $table->index('tran_id');
            $table->index('val_id');
            $table->index('payment_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('elsa_payments', function (Blueprint $table) {
            // Drop SSL Commerce specific fields
            $table->dropColumn([
                'val_id',
                'bank_tran_id',
                'card_issuer',
                'card_brand',
                'card_sub_brand',
                'card_issuer_country',
                'card_issuer_country_code',
                'store_amount',
                'verify_sign',
                'verify_key',
                'verify_sign_sha2',
                'currency_type',
                'currency_amount',
                'currency_rate',
                'base_fair',
                'value_a',
                'value_b',
                'value_c',
                'value_d',
                'risk_level',
                'risk_title',
                'gateway_response',
                'payment_processor',
                'ssl_response_data'
            ]);

            // Drop indexes
            $table->dropIndex(['tran_id']);
            $table->dropIndex(['val_id']);
            $table->dropIndex(['payment_status']);
        });
    }
};
