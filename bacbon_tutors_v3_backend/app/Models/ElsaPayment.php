<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ElsaPayment extends Model
{
    protected $fillable = [
        'name',
        'email',
        'phone',
        'organization',
        'address',
        'no_of_user',
        'elsa_package_id',
        'month',
        'total_amount',
        'unit_price',
        'discount',
        'grand_total',
        'user_id',
        'card_type',
        'currency',
        'tran_id',
        'payment_status',
        'is_promo_applied',
        'promo_id',
        'status',
        // SSL Commerce specific fields
        'val_id',
        'bank_tran_id',
        'card_issuer',
        'card_brand',
        'card_sub_brand',
        'card_issuer_country',
        'card_issuer_country_code',
        'store_amount',
        'verify_sign',
        'verify_key',
        'verify_sign_sha2',
        'currency_type',
        'currency_amount',
        'currency_rate',
        'base_fair',
        'value_a',
        'value_b',
        'value_c',
        'value_d',
        'risk_level',
        'risk_title',
        'gateway_response',
        'payment_processor',
        'ssl_response_data',
    ];

    protected $casts = [
        'ssl_response_data' => 'array',
        'is_promo_applied' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the package associated with this payment
     */
    public function package()
    {
        return $this->belongsTo(ElsaPackage::class, 'elsa_package_id');
    }

    /**
     * Get the user associated with this payment (if applicable)
     */
    public function user()
    {
        return $this->belongsTo(\App\Models\User::class, 'user_id');
    }

    /**
     * Scope for completed payments
     */
    public function scopeCompleted($query)
    {
        return $query->where('payment_status', 'completed');
    }

    /**
     * Scope for pending payments
     */
    public function scopePending($query)
    {
        return $query->where('payment_status', 'pending');
    }

    /**
     * Scope for failed payments
     */
    public function scopeFailed($query)
    {
        return $query->where('payment_status', 'failed');
    }
}
