<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\ElsaPayment;
use App\Models\ElsaPackage;
use App\Http\Requests\StorePaymentRequest;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    private $sslcommerzStoreId;
    private $sslcommerzStorePassword;
    private $sslcommerzApiUrl;
    private $sslcommerzValidationUrl;
    private $frontendUrl;

    public function __construct()
    {
        $this->sslcommerzStoreId = config('app.sslcommerz_store_id', env('SSLCOMMERZ_STORE_ID'));
        $this->sslcommerzStorePassword = config('app.sslcommerz_store_password', env('SSLCOMMERZ_STORE_PASSWORD'));
        $this->sslcommerzApiUrl = config('app.sslcommerz_api_url', env('SSLCOMMERZ_API_URL'));
        $this->sslcommerzValidationUrl = config('app.sslcommerz_validation_url', env('SSLCOMMERZ_VALIDATION_URL'));
        $this->frontendUrl = config('app.frontend_url', env('FRONTEND_URL', 'http://localhost:5174'));
    }

    /**
     * Initiate payment with SSL Commerce
     */
    public function pay(StorePaymentRequest $request)
    {
        try {
            // Get package details
            $package = ElsaPackage::findOrFail($request->elsa_package_id);

            // Calculate totals
            $totalAmount = $request->unit_price * $request->no_of_user * $request->month;
            $discount = $request->discount ?? 0;
            $grandTotal = $totalAmount - $discount;

            // Generate unique transaction ID
            $tranId = 'BB_' . time() . '_' . Str::random(6);

            // Create payment record
            $payment = ElsaPayment::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'organization' => $request->organization,
                'address' => $request->address,
                'no_of_user' => $request->no_of_user,
                'elsa_package_id' => $request->elsa_package_id,
                'month' => $request->month,
                'total_amount' => $totalAmount,
                'unit_price' => $request->unit_price,
                'discount' => $discount,
                'grand_total' => $grandTotal,
                'currency' => 'BDT',
                'tran_id' => $tranId,
                'payment_status' => 'pending',
                'status' => 'pending',
            ]);

            // Prepare SSL Commerce payment data
            $postData = [
                'store_id' => $this->sslcommerzStoreId,
                'store_passwd' => $this->sslcommerzStorePassword,
                'total_amount' => $grandTotal,
                'currency' => 'BDT',
                'tran_id' => $tranId,
                'success_url' => url('/api/payment/success'),
                'fail_url' => url('/api/payment/fail'),
                'cancel_url' => url('/api/payment/cancel'),
                'ipn_url' => url('/api/payment/ipn'),
                'cus_name' => $request->name,
                'cus_email' => $request->email,
                'cus_add1' => $request->address,
                'cus_city' => 'Dhaka',
                'cus_state' => 'Dhaka',
                'cus_postcode' => '1000',
                'cus_country' => 'Bangladesh',
                'cus_phone' => $request->phone,
                'ship_name' => $request->name,
                'ship_add1' => $request->address,
                'ship_city' => 'Dhaka',
                'ship_state' => 'Dhaka',
                'ship_postcode' => '1000',
                'ship_country' => 'Bangladesh',
                'product_name' => $package->duration ?? 'BacBon Package',
                'product_category' => 'Education',
                'product_profile' => 'general',
            ];

            Log::info('SSL Commerce Payment Initiation', [
                'tran_id' => $tranId,
                'amount' => $grandTotal,
                'customer' => $request->email
            ]);

            // Make request to SSL Commerce
            $response = Http::asForm()->post($this->sslcommerzApiUrl, $postData);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['status'] === 'SUCCESS') {
                    return response()->json([
                        'success' => true,
                        'payment_url' => $responseData['GatewayPageURL'],
                        'tran_id' => $tranId,
                        'message' => 'Payment initiated successfully'
                    ]);
                } else {
                    Log::error('SSL Commerce API Error', $responseData);
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to initiate payment: ' . ($responseData['failedreason'] ?? 'Unknown error')
                    ], Response::HTTP_BAD_REQUEST);
                }
            } else {
                Log::error('SSL Commerce HTTP Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Payment gateway is currently unavailable'
                ], Response::HTTP_SERVICE_UNAVAILABLE);
            }

        } catch (\Exception $e) {
            Log::error('Payment Initiation Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your payment'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Handle successful payment callback from SSL Commerce
     */
    public function success(Request $request)
    {
        try {
            Log::info('SSL Commerce Success Callback', $request->all());

            $tranId = $request->input('tran_id');
            $valId = $request->input('val_id');
            $amount = $request->input('amount');
            $status = $request->input('status');

            if (!$tranId) {
                Log::error('Success callback missing transaction ID', $request->all());
                return $this->redirectToFrontend('fail', 'Invalid transaction data');
            }

            // Find payment record
            $payment = ElsaPayment::where('tran_id', $tranId)->first();
            if (!$payment) {
                Log::error('Payment record not found', ['tran_id' => $tranId]);
                return $this->redirectToFrontend('fail', 'Payment record not found');
            }

            // Check if already processed
            if ($payment->payment_status === 'completed') {
                Log::info('Payment already processed', ['tran_id' => $tranId]);
                return $this->redirectToFrontend('success', 'Payment already completed');
            }

            // Validate payment status
            $validStatuses = ['VALID', 'VALIDATED', 'SUCCESS', 'SUCCESSFUL'];
            if (!in_array(strtoupper($status), $validStatuses)) {
                Log::warning('Invalid payment status', ['status' => $status, 'tran_id' => $tranId]);
                return $this->redirectToFrontend('fail', 'Payment validation failed');
            }

            // Validate amount
            if ($amount && abs($payment->grand_total - floatval($amount)) > 0.01) {
                Log::error('Amount mismatch', [
                    'expected' => $payment->grand_total,
                    'received' => $amount,
                    'tran_id' => $tranId
                ]);
                return $this->redirectToFrontend('fail', 'Payment amount mismatch');
            }

            // Validate with SSL Commerce if val_id is provided
            if ($valId) {
                $validationResult = $this->validateWithSSLCommerz($valId, $tranId, $amount);
                if (!$validationResult['valid']) {
                    Log::error('SSL Commerce validation failed', $validationResult);
                    return $this->redirectToFrontend('fail', 'Payment validation failed');
                }
            }

            // Update payment status
            $payment->update([
                'payment_status' => 'completed',
                'status' => 'completed',
                'card_type' => $request->input('card_type'),
                'updated_at' => now()
            ]);

            Log::info('Payment completed successfully', ['tran_id' => $tranId]);
            return $this->redirectToFrontend('success', 'Payment completed successfully');

        } catch (\Exception $e) {
            Log::error('Success callback error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return $this->redirectToFrontend('fail', 'An error occurred processing your payment');
        }
    }

    /**
     * Handle failed payment callback from SSL Commerce
     */
    public function fail(Request $request)
    {
        try {
            Log::info('SSL Commerce Fail Callback', $request->all());

            $tranId = $request->input('tran_id');
            $status = $request->input('status');

            if ($tranId) {
                $payment = ElsaPayment::where('tran_id', $tranId)->first();
                if ($payment && $payment->payment_status !== 'failed') {
                    $payment->update([
                        'payment_status' => 'failed',
                        'status' => 'failed',
                        'updated_at' => now()
                    ]);
                }
            }

            return $this->redirectToFrontend('fail', 'Payment failed');

        } catch (\Exception $e) {
            Log::error('Fail callback error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return $this->redirectToFrontend('fail', 'Payment failed');
        }
    }

    /**
     * Handle cancelled payment callback from SSL Commerce
     */
    public function cancel(Request $request)
    {
        try {
            Log::info('SSL Commerce Cancel Callback', $request->all());

            $tranId = $request->input('tran_id');

            if ($tranId) {
                $payment = ElsaPayment::where('tran_id', $tranId)->first();
                if ($payment && $payment->payment_status !== 'cancelled') {
                    $payment->update([
                        'payment_status' => 'cancelled',
                        'status' => 'cancelled',
                        'updated_at' => now()
                    ]);
                }
            }

            return $this->redirectToFrontend('cancel', 'Payment cancelled');

        } catch (\Exception $e) {
            Log::error('Cancel callback error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return $this->redirectToFrontend('cancel', 'Payment cancelled');
        }
    }

    /**
     * Handle IPN (Instant Payment Notification) from SSL Commerce
     */
    public function ipn(Request $request)
    {
        try {
            Log::info('SSL Commerce IPN Callback', $request->all());

            $tranId = $request->input('tran_id');
            $valId = $request->input('val_id');
            $amount = $request->input('amount');
            $status = $request->input('status');

            if (!$tranId || !$valId) {
                Log::error('IPN missing required data', $request->all());
                return response('INVALID', 400);
            }

            // Find payment record
            $payment = ElsaPayment::where('tran_id', $tranId)->first();
            if (!$payment) {
                Log::error('IPN: Payment record not found', ['tran_id' => $tranId]);
                return response('INVALID', 400);
            }

            // Validate with SSL Commerce
            $validationResult = $this->validateWithSSLCommerz($valId, $tranId, $amount);
            if (!$validationResult['valid']) {
                Log::error('IPN: SSL Commerce validation failed', $validationResult);
                return response('INVALID', 400);
            }

            // Update payment status if not already processed
            if ($payment->payment_status !== 'completed') {
                $validStatuses = ['VALID', 'VALIDATED', 'SUCCESS', 'SUCCESSFUL'];
                if (in_array(strtoupper($status), $validStatuses)) {
                    $payment->update([
                        'payment_status' => 'completed',
                        'status' => 'completed',
                        'updated_at' => now()
                    ]);
                    Log::info('IPN: Payment status updated to completed', ['tran_id' => $tranId]);
                }
            }

            return response('VALID', 200);

        } catch (\Exception $e) {
            Log::error('IPN callback error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response('INVALID', 500);
        }
    }

    /**
     * Validate payment with SSL Commerce API
     */
    private function validateWithSSLCommerz($valId, $tranId, $amount)
    {
        try {
            $validationData = [
                'val_id' => $valId,
                'store_id' => $this->sslcommerzStoreId,
                'store_passwd' => $this->sslcommerzStorePassword,
                'format' => 'json'
            ];

            $response = Http::asForm()
                ->timeout(30)
                ->post($this->sslcommerzValidationUrl, $validationData);

            if ($response->successful()) {
                $responseData = $response->json();

                Log::info('SSL Commerce Validation Response', [
                    'val_id' => $valId,
                    'response' => $responseData
                ]);

                // Check validation status
                $validStatuses = ['VALID', 'VALIDATED', 'SUCCESS', 'SUCCESSFUL'];
                $isValidStatus = in_array(strtoupper($responseData['status'] ?? ''), $validStatuses);

                // Check transaction ID match
                $tranIdMatch = ($responseData['tran_id'] ?? '') === $tranId;

                // Check amount match (if provided)
                $amountMatch = true;
                if ($amount && isset($responseData['amount'])) {
                    $amountMatch = abs(floatval($responseData['amount']) - floatval($amount)) < 0.01;
                }

                return [
                    'valid' => $isValidStatus && $tranIdMatch && $amountMatch,
                    'status' => $responseData['status'] ?? 'unknown',
                    'tran_id_match' => $tranIdMatch,
                    'amount_match' => $amountMatch,
                    'response' => $responseData
                ];
            } else {
                Log::error('SSL Commerce Validation HTTP Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return ['valid' => false, 'error' => 'HTTP error'];
            }

        } catch (\Exception $e) {
            Log::error('SSL Commerce Validation Exception', [
                'error' => $e->getMessage(),
                'val_id' => $valId
            ]);
            return ['valid' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Redirect to frontend with payment status
     */
    private function redirectToFrontend($status, $message = '')
    {
        $url = $this->frontendUrl . '/payment-status';
        $params = http_build_query([
            'status' => $status,
            'message' => $message
        ]);

        return redirect($url . '?' . $params);
    }
}
