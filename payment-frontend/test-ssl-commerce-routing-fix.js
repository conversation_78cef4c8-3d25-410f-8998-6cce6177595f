#!/usr/bin/env node

/**
 * Test script to verify SSL Commerce routing fix
 * Tests the scenario where successful payments are incorrectly sent to fail endpoint
 */

const API_BASE_URL = 'http://127.0.0.1:8000/api';

async function makeRequest(method, endpoint, data = null, description = '') {
  try {
    console.log(`\n🧪 ${description}`);
    console.log(`${method} ${API_BASE_URL}${endpoint}`);
    
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'SSL-Commerce-Test/1.0'
      },
    };

    let url = `${API_BASE_URL}${endpoint}`;
    
    if (method === 'GET' && data) {
      const queryString = new URLSearchParams(data).toString();
      url += `?${queryString}`;
      console.log(`Query: ${queryString}`);
    } else if (data) {
      options.body = JSON.stringify(data);
      console.log(`Body: ${JSON.stringify(data, null, 2)}`);
    }

    const response = await fetch(url, options);
    const result = await response.text();
    
    console.log(`Status: ${response.status}`);
    console.log(`Response: ${result.substring(0, 500)}${result.length > 500 ? '...' : ''}`);
    
    return { status: response.status, data: result, success: response.ok };
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    return { error: error.message };
  }
}

async function testSSLCommerceRoutingFix() {
  console.log('🚀 Testing SSL Commerce Routing Fix');
  console.log('===================================');
  console.log('This tests the fix for successful payments incorrectly sent to fail endpoint');
  
  const testTransactionId = 'TRX_20250618151632_1L8RX9';
  
  // Test 1: Check current payment status
  console.log('\n📋 STEP 1: Check Current Payment Status');
  await makeRequest('GET', `/payment/status/${testTransactionId}`, null, 'Current payment status');

  // Test 2: Test successful payment sent to fail endpoint (the main issue)
  console.log('\n📋 STEP 2: Test Successful Payment Sent to Fail Endpoint');
  await makeRequest('POST', '/payment/fail', {
    tran_id: testTransactionId,
    status: 'success',  // This is the key - success status sent to fail endpoint
    amount: '499.99',
    payment_method: 'Nagad'
  }, 'SUCCESS status sent to FAIL endpoint (should redirect to success)');

  // Test 3: Verify payment status was updated correctly
  console.log('\n📋 STEP 3: Verify Payment Status After Fix');
  await makeRequest('GET', `/payment/status/${testTransactionId}`, null, 'Payment status after routing fix');

  // Test 4: Test various success status formats sent to fail endpoint
  console.log('\n📋 STEP 4: Test Different Success Status Formats');
  
  const successStatuses = ['success', 'SUCCESS', 'valid', 'VALID', 'successful', 'SUCCESSFUL', 'complete', 'COMPLETE'];
  
  for (const status of successStatuses) {
    await makeRequest('POST', '/payment/fail', {
      tran_id: testTransactionId,
      status: status,
      amount: '499.99'
    }, `Testing "${status}" status sent to fail endpoint`);
  }

  // Test 5: Test genuine failure (should still go to fail)
  console.log('\n📋 STEP 5: Test Genuine Failure');
  await makeRequest('POST', '/payment/fail', {
    tran_id: testTransactionId,
    status: 'failed',
    amount: '499.99',
    error: 'Card declined'
  }, 'Genuine failure (should stay on fail endpoint)');

  // Test 6: Test amount mismatch in success-to-fail scenario
  console.log('\n📋 STEP 6: Test Amount Mismatch Detection');
  await makeRequest('POST', '/payment/fail', {
    tran_id: testTransactionId,
    status: 'success',
    amount: '100.00',  // Wrong amount
    payment_method: 'Nagad'
  }, 'Success status with wrong amount (should reject)');

  // Test 7: Test with missing amount
  console.log('\n📋 STEP 7: Test Success Without Amount');
  await makeRequest('POST', '/payment/fail', {
    tran_id: testTransactionId,
    status: 'success',
    payment_method: 'Nagad'
    // No amount provided
  }, 'Success status without amount (should accept)');

  console.log('\n✅ SSL Commerce Routing Fix Tests Complete!');
  console.log('\n📝 Expected Results:');
  console.log('- Success statuses sent to fail endpoint should redirect to success page');
  console.log('- Payment status should be updated to "successful"');
  console.log('- Payment status should include "VALID_FROM_FAIL_ENDPOINT" indicator');
  console.log('- Genuine failures should still go to fail page');
  console.log('- Amount mismatches should be rejected even with success status');
  
  console.log('\n🔧 SSL Commerce Configuration Fix Needed:');
  console.log('Update your SSL Commerce merchant dashboard:');
  console.log('- Success URL: https://your-domain.com/api/payment/success');
  console.log('- Fail URL: https://your-domain.com/api/payment/fail');
  console.log('- Cancel URL: https://your-domain.com/api/payment/cancel');
  console.log('- IPN URL: https://your-domain.com/api/payment/ipn');
  
  console.log('\n📊 Monitoring Commands:');
  console.log('grep "ROUTING ERROR" storage/logs/laravel.log');
  console.log('grep "VALID_FROM_FAIL_ENDPOINT" storage/logs/laravel.log');
  console.log('grep "converted from fail to success" storage/logs/laravel.log');
}

async function testSpecificNagadPayment() {
  console.log('\n🎯 Testing Specific Nagad Payment Scenario');
  console.log('==========================================');
  
  // Simulate the exact scenario from your data
  const nagadPaymentData = {
    tran_id: 'TRX_20250618151632_1L8RX9',
    status: 'success',  // lowercase as SSL Commerce sends it
    amount: '499.99',
    payment_method: 'Nagad',
    card_type: 'NAGAD-Nagad',
    card_issuer: 'Nagad Bangladesh',
    ip_address: '************'
  };

  console.log('\n🔍 Simulating your exact Nagad payment data:');
  console.log(JSON.stringify(nagadPaymentData, null, 2));

  // Test on fail endpoint (where SSL Commerce is incorrectly sending it)
  await makeRequest('POST', '/payment/fail', nagadPaymentData, 'Your exact Nagad payment data sent to fail endpoint');

  // Check final status
  await makeRequest('GET', `/payment/status/${nagadPaymentData.tran_id}`, null, 'Final payment status');
}

// Run tests based on command line arguments
if (process.argv[2] === 'nagad') {
  testSpecificNagadPayment().catch(console.error);
} else {
  testSSLCommerceRoutingFix().catch(console.error);
}

console.log('\n💡 Usage:');
console.log('node test-ssl-commerce-routing-fix.js        # Run full routing fix tests');
console.log('node test-ssl-commerce-routing-fix.js nagad  # Test specific Nagad scenario');
