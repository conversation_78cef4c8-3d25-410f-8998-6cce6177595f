import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { apiEndpoints } from '../services/api';
import { CheckCircle, XCircle, AlertCircle, Home, RefreshCw } from 'lucide-react';

const PaymentStatus = ({ type }) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [paymentData, setPaymentData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const params = Object.fromEntries(searchParams.entries());

    // Handle redirect from Laravel backend (new format)
    if (!type && params.status) {
      const redirectType = params.status;
      const message = params.message || '';

      setPaymentData({
        status: redirectType,
        message: message,
        redirect_from_backend: true
      });
      setLoading(false);
      return;
    }

    // Handle direct API calls (legacy format)
    if (type === 'success' && params.tran_id && params.val_id) {
      handlePaymentSuccess(params);
    } else if (type === 'fail' && params.tran_id) {
      handlePaymentFail(params);
    } else if (type === 'cancel' && params.tran_id) {
      handlePaymentCancel(params);
    } else if (!type && !params.status) {
      setError('Invalid payment status parameters');
      setLoading(false);
    }
  }, [searchParams, type]);

  const handlePaymentSuccess = async (params) => {
    try {
      const response = await apiEndpoints.paymentSuccess(params);
      setPaymentData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to verify payment');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentFail = async (params) => {
    try {
      const response = await apiEndpoints.paymentFail(params);
      setPaymentData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to process payment failure');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentCancel = async (params) => {
    try {
      const response = await apiEndpoints.paymentCancel(params);
      setPaymentData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to process payment cancellation');
    } finally {
      setLoading(false);
    }
  };

  const getStatusConfig = () => {
    // Handle redirect from Laravel backend
    if (paymentData?.redirect_from_backend) {
      const status = paymentData.status;
      const message = paymentData.message;

      switch (status) {
        case 'success':
          return {
            icon: CheckCircle,
            color: 'text-green-500',
            bgColor: 'bg-green-50',
            borderColor: 'border-green-200',
            title: 'Payment Successful!',
            message: message || 'Your payment has been processed successfully.',
          };
        case 'fail':
          return {
            icon: XCircle,
            color: 'text-red-500',
            bgColor: 'bg-red-50',
            borderColor: 'border-red-200',
            title: 'Payment Failed',
            message: message || 'Your payment could not be processed.',
          };
        case 'cancel':
          return {
            icon: AlertCircle,
            color: 'text-yellow-500',
            bgColor: 'bg-yellow-50',
            borderColor: 'border-yellow-200',
            title: 'Payment Cancelled',
            message: message || 'You have cancelled the payment process.',
          };
        default:
          return {
            icon: AlertCircle,
            color: 'text-gray-500',
            bgColor: 'bg-gray-50',
            borderColor: 'border-gray-200',
            title: 'Unknown Status',
            message: message || 'Payment status is unknown.',
          };
      }
    }

    // Handle direct component type (legacy)
    switch (type) {
      case 'success':
        return {
          icon: CheckCircle,
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: 'Payment Successful!',
          message: 'Your payment has been processed successfully.',
        };
      case 'fail':
        return {
          icon: XCircle,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Payment Failed',
          message: 'Your payment could not be processed.',
        };
      case 'cancel':
        return {
          icon: AlertCircle,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          title: 'Payment Cancelled',
          message: 'You have cancelled the payment process.',
        };
      default:
        return {
          icon: AlertCircle,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Unknown Status',
          message: 'Payment status is unknown.',
        };
    }
  };

  const config = getStatusConfig();
  const IconComponent = config.icon;

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Processing payment status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className={`${config.bgColor} ${config.borderColor} border rounded-lg p-8 text-center`}>
          <IconComponent className={`w-16 h-16 ${config.color} mx-auto mb-4`} />
          
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {config.title}
          </h1>
          
          <p className="text-gray-600 mb-6">
            {config.message}
          </p>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}

          {paymentData && (
            <div className="bg-white rounded-lg p-4 mb-6 text-left">
              <h3 className="font-semibold mb-3">Payment Details</h3>
              <div className="space-y-2 text-sm">
                {paymentData.transaction_id && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Transaction ID:</span>
                    <span className="font-mono">{paymentData.transaction_id}</span>
                  </div>
                )}
                {paymentData.amount && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount:</span>
                    <span>৳{paymentData.amount}</span>
                  </div>
                )}
                {paymentData.currency && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Currency:</span>
                    <span>{paymentData.currency}</span>
                  </div>
                )}
                {paymentData.bank_tran_id && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Bank Transaction ID:</span>
                    <span className="font-mono">{paymentData.bank_tran_id}</span>
                  </div>
                )}
                {paymentData.card_type && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Card Type:</span>
                    <span>{paymentData.card_type}</span>
                  </div>
                )}
                {paymentData.payment_status && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={`font-semibold ${
                      paymentData.payment_status === 'VALID' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {paymentData.payment_status}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="space-y-3">
            <button
              onClick={() => navigate('/')}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 flex items-center justify-center"
            >
              <Home className="w-4 h-4 mr-2" />
              Back to Home
            </button>
            
            {(type === 'fail' || (paymentData?.redirect_from_backend && paymentData?.status === 'fail')) && (
              <button
                onClick={() => navigate('/')}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 flex items-center justify-center"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </button>
            )}
          </div>
        </div>

        {(type === 'success' || (paymentData?.redirect_from_backend && paymentData?.status === 'success')) && (
          <div className="mt-6 text-center text-sm text-gray-600">
            <p>A confirmation email has been sent to your registered email address.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentStatus;
